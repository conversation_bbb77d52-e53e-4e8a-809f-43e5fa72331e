'use client';

import { AlertTriangle } from 'lucide-react';

import { TonPriceDisplay } from '@/components/shared/ton-price-display';

import type { OrderEntity } from '@/constants/core.constants';

interface SellPriceDetailsProps {
  order: OrderEntity;
  className?: string;
}

export function SellPriceDetails({
  order,
  className = '',
}: SellPriceDetailsProps) {
  const hasSecondaryPrice =
    order.secondaryMarketPrice && order.secondaryMarketPrice > 0;
  const currentPrice = hasSecondaryPrice
    ? order.secondaryMarketPrice
    : order.price;

  // Calculate if this is a risky price using the same logic as ResellPriceWarning
  const calculateLockedCollateral = () => {
    if (!order?.fees) return 0;

    const buyerLockPercentage = order.fees.buyer_locked_percentage / 10000;
    const sellerLockPercentage = order.fees.seller_locked_percentage / 10000;

    const buyerLocked = order.price * buyerLockPercentage;
    const sellerLocked = order.price * sellerLockPercentage;

    return buyerLocked + sellerLocked;
  };

  const totalLockedCollateral = calculateLockedCollateral();
  const isHighRiskPrice =
    hasSecondaryPrice &&
    order.secondaryMarketPrice! > 0 &&
    order.secondaryMarketPrice! > totalLockedCollateral * 2;

  return (
    <div className={`flex items-center justify-center gap-2 ${className}`}>
      <div className="flex items-center gap-1">
        <TonPriceDisplay
          amount={currentPrice ?? 0}
          size={24}
          className="text-2xl font-bold text-[#f5f5f5]"
          showUnit
        />
        {hasSecondaryPrice && (
          <span className="text-sm text-[#708499] line-through ml-1">
            ({order.price})
          </span>
        )}
      </div>

      {isHighRiskPrice && (
        <AlertTriangle
          className="w-5 h-5 text-yellow-500 cursor-help"
          title="Risky price"
        />
      )}
    </div>
  );
}
