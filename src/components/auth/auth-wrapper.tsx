'use client';

import { ReactElement, cloneElement, useState } from 'react';

import { useRootContext } from '@/root-context';

import { LoginModal } from './login-modal';

interface AuthWrapperProps {
  children: ReactElement;
  showModalOnClick?: boolean;
}

export function AuthWrapper({
  children,
  showModalOnClick = true,
}: AuthWrapperProps) {
  const { currentUser } = useRootContext();
  const [showLoginModal, setShowLoginModal] = useState(false);

  if (!currentUser && showModalOnClick) {
    const handleClickCapture = (e: React.MouseEvent) => {
      e.preventDefault();
      e.stopPropagation();
      setShowLoginModal(true);
    };

    return (
      <>
        {cloneElement(children, {
          onClickCapture: handleClickCapture,
          style: { cursor: 'pointer' },
        })}
        <LoginModal open={showLoginModal} onOpenChange={setShowLoginModal} />
      </>
    );
  }

  return children;
}
